<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>辩论倒计时</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #1a1a1a;
            color: white;
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            overflow: hidden; /* 防止滚动条 */
        }

        .container {
            height: 100vh;
            display: flex;
        }

        .side {
            flex: 1;
            min-width: 20vw; /* 增加最小宽度，确保在极端比分下仍能看到文字 */
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
            transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1); /* 弹性过渡效果 */
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.5) inset;
        }

        .left {
            background: linear-gradient(135deg, #ff0000, #990000);
            border-right: 2px solid #fff;
            z-index: 2;
            position: relative;
            overflow: hidden;
        }

        .left::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 0, 0, 0.8), rgba(153, 0, 0, 0.8));
            opacity: 0.5; /* 降低不透明度 */
            z-index: -1;
            mix-blend-mode: overlay; /* 添加混合模式 */
        }

        /* 正方扫光效果 - 主扫光 */
        .left::before {
            content: '';
            position: absolute;
            top: 0;
            left: -150%;
            width: 200%; /* 加宽光柱到200% */
            height: 100%;
            background:
                linear-gradient(to right,
                    transparent 0%,
                    transparent 15%,
                    rgba(255, 200, 200, 0.01) 25%,
                    rgba(255, 220, 220, 0.05) 35%,
                    rgba(255, 255, 255, 1) 50%,
                    rgba(255, 220, 220, 0.05) 65%,
                    rgba(255, 200, 200, 0.01) 75%,
                    transparent 85%,
                    transparent 100%
                ),
                linear-gradient(to right,
                    transparent 0%,
                    transparent 20%,
                    rgba(255, 150, 150, 0.02) 30%,
                    rgba(255, 180, 180, 0.08) 40%,
                    rgba(255, 255, 255, 1) 50%,
                    rgba(255, 180, 180, 0.08) 60%,
                    rgba(255, 150, 150, 0.02) 70%,
                    transparent 80%,
                    transparent 100%
                ),
                linear-gradient(to bottom,
                    transparent 30%,
                    rgba(255, 100, 100, 0.05) 50%,
                    transparent 70%
                );
            z-index: 1;
            animation: sweep-right 3s linear infinite;
            mix-blend-mode: overlay;
            box-shadow:
                0 0 40px rgba(255, 0, 0, 0.1) inset,
                0 0 80px rgba(255, 0, 0, 0.05) inset;
        }

        /* 移除正方细扫光效果 */

        /* 正方扫光效果 - 光晕 */
        .left-glow {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(
                    circle at 30% 50%,
                    rgba(255, 100, 100, 0.15),
                    transparent 70%
                ),
                radial-gradient(
                    circle at 20% 30%,
                    rgba(255, 150, 150, 0.08),
                    transparent 50%
                ),
                radial-gradient(
                    circle at 40% 70%,
                    rgba(255, 50, 50, 0.12),
                    transparent 60%
                ),
                radial-gradient(
                    ellipse at 25% 50%,
                    rgba(255, 200, 200, 0.05),
                    transparent 80%
                ),
                linear-gradient(to right,
                    transparent 20%,
                    rgba(255, 0, 0, 0.03) 50%,
                    transparent 80%
                );
            z-index: 0;
            animation: glow-pulse 4s ease-in-out infinite alternate;
            mix-blend-mode: overlay;
            pointer-events: none;
            filter: blur(1px);
        }

        @keyframes sweep-right {
            0% { transform: translateX(0); }
            100% { transform: translateX(300%); }
        }

        /* 移除不需要的动画 */

        @keyframes glow-pulse {
            0% { opacity: 0.5; transform: scale(0.98) translate(-2px, -2px); filter: blur(1px); }
            25% { opacity: 0.6; transform: scale(0.99) translate(0px, -1px); }
            50% { opacity: 0.7; transform: scale(1.0) translate(2px, 0px); filter: blur(1.5px); }
            75% { opacity: 0.75; transform: scale(1.01) translate(0px, 1px); }
            100% { opacity: 0.8; transform: scale(1.02) translate(-1px, 2px); filter: blur(2px); }
        }

        .right {
            background: linear-gradient(135deg, #0000ff, #000099);
            border-left: 2px solid #fff;
            z-index: 1;
            position: relative;
            overflow: hidden;
        }

        .right::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 0, 255, 0.8), rgba(0, 0, 153, 0.8));
            opacity: 0.5; /* 降低不透明度 */
            z-index: -1;
            mix-blend-mode: overlay; /* 添加混合模式 */
        }

        /* 反方扫光效果 - 主扫光 */
        .right::before {
            content: '';
            position: absolute;
            top: 0;
            right: -150%;
            width: 200%; /* 加宽光柱到200% */
            height: 100%;
            background:
                linear-gradient(to left,
                    transparent 0%,
                    transparent 15%,
                    rgba(200, 200, 255, 0.01) 25%,
                    rgba(220, 220, 255, 0.05) 35%,
                    rgba(255, 255, 255, 1) 50%,
                    rgba(220, 220, 255, 0.05) 65%,
                    rgba(200, 200, 255, 0.01) 75%,
                    transparent 85%,
                    transparent 100%
                ),
                linear-gradient(to left,
                    transparent 0%,
                    transparent 20%,
                    rgba(150, 150, 255, 0.02) 30%,
                    rgba(180, 180, 255, 0.08) 40%,
                    rgba(255, 255, 255, 1) 50%,
                    rgba(180, 180, 255, 0.08) 60%,
                    rgba(150, 150, 255, 0.02) 70%,
                    transparent 80%,
                    transparent 100%
                ),
                linear-gradient(to bottom,
                    transparent 30%,
                    rgba(100, 100, 255, 0.05) 50%,
                    transparent 70%
                );
            z-index: 1;
            animation: sweep-left 3s linear infinite;
            mix-blend-mode: overlay;
            box-shadow:
                0 0 40px rgba(0, 0, 255, 0.1) inset,
                0 0 80px rgba(0, 0, 255, 0.05) inset;
        }

        /* 反方扫光效果 - 细扫光 */
        /* 移除反方细扫光效果 */

        /* 反方扫光效果 - 光晕 */
        .right-glow {
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(
                    circle at 70% 50%,
                    rgba(100, 100, 255, 0.15),
                    transparent 70%
                ),
                radial-gradient(
                    circle at 80% 30%,
                    rgba(150, 150, 255, 0.08),
                    transparent 50%
                ),
                radial-gradient(
                    circle at 60% 70%,
                    rgba(50, 50, 255, 0.12),
                    transparent 60%
                ),
                radial-gradient(
                    ellipse at 75% 50%,
                    rgba(200, 200, 255, 0.05),
                    transparent 80%
                ),
                linear-gradient(to left,
                    transparent 20%,
                    rgba(0, 0, 255, 0.03) 50%,
                    transparent 80%
                );
            z-index: 0;
            animation: glow-pulse 4s ease-in-out infinite alternate 2s;
            mix-blend-mode: overlay;
            pointer-events: none;
            filter: blur(1px);
        }

        @keyframes sweep-left {
            0% { transform: translateX(0); }
            100% { transform: translateX(-300%); }
        }

        /* 移除不需要的动画 */

        /* 添加对抗线 */
        .divider {
            position: absolute;
            left: var(--divider-position, 50%);
            top: 0;
            height: 100%;
            width: 4px;
            background: rgba(255, 255, 255, 0.9);
            transform: translateX(-50%);
            z-index: 3;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.9);
            transition: left 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
            overflow: hidden;
        }

        .divider::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom,
                        transparent 0%,
                        rgba(255, 255, 255, 0.7) 50%,
                        transparent 100%);
            animation: divider-flow 3s linear infinite;
        }

        @keyframes divider-flow {
            0% { transform: translateY(-100%); }
            100% { transform: translateY(100%); }
        }

        .percentage {
            position: relative;
            font-weight: 900; /* 更粗的字体 */
            text-shadow: 0 0 15px rgba(0, 0, 0, 0.7);
            letter-spacing: -2px;
            filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.3));
            animation: percentage-float 3s ease-in-out infinite;
        }

        @keyframes percentage-float {
            0% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0); }
        }

        .percentage.left-text {
            font-size: clamp(3rem, calc(6vw * var(--left-scale, 1)), 8rem);
            transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
            color: #fff;
            transform-origin: center right;
            text-shadow: 0 0 15px rgba(255, 0, 0, 0.7);
            animation-delay: 0s; /* 左侧百分比无延迟 */
        }

        .percentage.right-text {
            font-size: clamp(3rem, calc(6vw * var(--right-scale, 1)), 8rem);
            transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
            color: #fff;
            transform-origin: center left;
            text-shadow: 0 0 15px rgba(0, 0, 255, 0.7);
            animation-delay: 0.5s; /* 右侧百分比延迟0.5秒 */
        }

        /* 移除百分比文字的闪光效果 */

        .count {
            font-size: 3.5rem;
            font-weight: bold;
            opacity: 0.9;
            margin-top: 1.5rem;
            background: rgba(0, 0, 0, 0.4);
            padding: 0.5rem 1.5rem;
            border-radius: 50px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.5), inset 0 0 10px rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: count-float 3s ease-in-out infinite;
        }

        @keyframes count-float {
            0% { transform: translateY(0); }
            50% { transform: translateY(-8px); }
            100% { transform: translateY(0); }
        }

        /* 左侧count延迟 */
        .left .count {
            animation-delay: 0.3s; /* 左侧count延迟0.3秒 */
        }

        /* 右侧count延迟 */
        .right .count {
            animation-delay: 0.8s; /* 右侧count延迟0.8秒 */
        }

        /* 当数据变化时的特效 */
        .count.changed {
            transform: scale(1.2);
            animation: count-pulse 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        @keyframes count-pulse {
            0% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.3); opacity: 1; }
            100% { transform: scale(1.2); opacity: 0.9; }
        }

        .label {
            font-size: 2.5rem;
            font-weight: 800;
            position: absolute;
            top: 2rem;
            padding: 0.5rem 2rem;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 50px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), inset 0 0 10px rgba(255, 255, 255, 0.1);
            text-transform: uppercase;
            letter-spacing: 2px;
            /* 移除浮动动画 */
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 移除浮动动画 */

        .left .label {
            background: rgba(0, 0, 0, 0.3);
        }

        .right .label {
            background: rgba(0, 0, 0, 0.3);
        }

        /* 移除进度条样式 */

        .total {
            position: fixed;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            font-size: 3.5rem;
            font-weight: bold;
            color: #fff;
            background: rgba(0, 0, 0, 0.7);
            padding: 0.8rem 2.5rem;
            border-radius: 50px;
            box-shadow: 0 0 30px rgba(255, 255, 255, 0.3), inset 0 0 15px rgba(255, 255, 255, 0.1);
            z-index: 10;
            text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(5px);
        }

        /* 添加VS标志 - 参考图片样式 */
        .vs-badge {
            position: absolute;
            top: 50%;
            left: var(--divider-position, 50%);
            transform: translate(-50%, -50%);
            background: transparent;
            color: #fff;
            font-size: 4.5rem;
            font-weight: 900;
            padding: 0.5rem;
            z-index: 5;
            text-transform: uppercase;
            transition: left 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
            letter-spacing: -3px;
            filter: drop-shadow(0 0 5px rgba(0, 162, 255, 0.5));
        }

        .vs-badge::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 150%;
            height: 150%;
            background: radial-gradient(circle, rgba(0, 162, 255, 0.6) 0%, rgba(0, 162, 255, 0.2) 40%, transparent 70%);
            transform: translate(-50%, -50%);
            z-index: -1;
            border-radius: 50%;
            filter: blur(8px);
            animation: pulse-glow 4s ease-in-out infinite alternate;
        }

        @keyframes pulse-glow {
            0% { opacity: 0.6; width: 140%; height: 140%; }
            100% { opacity: 0.8; width: 160%; height: 160%; }
        }

        .vs-badge::after {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            width: 2px;
            height: 200%;
            background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.8), transparent);
            transform: translateX(-50%);
            z-index: -2;
            filter: blur(3px);
        }

        /* VS文字样式 */
        .vs-text {
            display: inline-block;
            position: relative;
            z-index: 2;
            font-weight: 900;
            letter-spacing: -2px;
            color: #ffffff;
            text-shadow:
                0 0 2px #ffffff,
                0 0 5px rgba(0, 162, 255, 0.8);
            animation: text-pulse 3s ease-in-out infinite alternate;
        }

        @keyframes text-pulse {
            0% { text-shadow: 0 0 2px #ffffff, 0 0 5px rgba(0, 162, 255, 0.8); }
            100% { text-shadow: 0 0 4px #ffffff, 0 0 8px rgba(0, 162, 255, 0.9); }
        }

        @keyframes gradient-flow {
            0% { background-position: 0% 50%; }
            100% { background-position: 400% 50%; }
        }

        /* 斜线样式 */
        .vs-slash {
            display: inline-block;
            position: relative;
            z-index: 3;
            font-weight: 900;
            transform: scale(1.2, 1.5);
            margin: 0 -5px;
            color: #00a2ff;
            text-shadow:
                0 0 2px #ffffff,
                0 0 4px #ffffff,
                0 0 8px rgba(0, 162, 255, 1),
                0 0 12px rgba(0, 162, 255, 0.8);
            animation: slash-pulse 3s ease-in-out infinite alternate;
        }

        @keyframes slash-pulse {
            0% {
                opacity: 0.9;
                transform: scale(1.2, 1.5);
                text-shadow:
                    0 0 2px #ffffff,
                    0 0 4px #ffffff,
                    0 0 8px rgba(0, 162, 255, 1),
                    0 0 12px rgba(0, 162, 255, 0.8);
            }
            100% {
                opacity: 1;
                transform: scale(1.3, 1.6);
                text-shadow:
                    0 0 3px #ffffff,
                    0 0 6px #ffffff,
                    0 0 10px rgba(0, 162, 255, 1),
                    0 0 15px rgba(0, 162, 255, 0.8);
            }
        }

        /* 光束效果 */
        .vs-light-beam {
            position: absolute;
            top: 50%;
            left: -50%;
            width: 200%;
            height: 3px;
            background: linear-gradient(to right,
                transparent,
                rgba(255, 255, 255, 0.3),
                rgba(255, 255, 255, 0.7),
                rgba(0, 162, 255, 0.9),
                rgba(255, 255, 255, 0.7),
                rgba(255, 255, 255, 0.3),
                transparent);
            transform: translateY(-50%) rotate(-25deg);
            z-index: 1;
            filter: blur(1px);
            box-shadow: 0 0 5px rgba(0, 162, 255, 0.6);
            animation: beam-flash 3s infinite;
        }

        @keyframes beam-flash {
            0%, 100% { opacity: 0.7; filter: blur(1px); }
            50% { opacity: 1; filter: blur(0.5px); }
        }

        @keyframes vs-glow {
            0% {
                text-shadow:
                    0 0 10px rgba(255, 255, 255, 0.8),
                    0 0 20px rgba(255, 255, 255, 0.8),
                    0 0 30px rgba(0, 162, 255, 0.8),
                    0 0 40px rgba(0, 162, 255, 0.6),
                    0 0 50px rgba(0, 162, 255, 0.4),
                    0 0 60px rgba(0, 162, 255, 0.2);
                transform: translate(-50%, -50%) scale(1);
            }
            100% {
                text-shadow:
                    0 0 15px rgba(255, 255, 255, 0.9),
                    0 0 25px rgba(255, 255, 255, 0.9),
                    0 0 35px rgba(0, 162, 255, 0.9),
                    0 0 45px rgba(0, 162, 255, 0.7),
                    0 0 55px rgba(0, 162, 255, 0.5),
                    0 0 65px rgba(0, 162, 255, 0.3);
                transform: translate(-50%, -50%) scale(1.1);
            }
        }

        /* 增强的脉冲动画 */
        @keyframes pulse {
            0% { transform: scale(1); filter: brightness(1); }
            50% { transform: scale(1.1); filter: brightness(1.3); }
            100% { transform: scale(1); filter: brightness(1); }
        }

        .changed {
            animation: pulse 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        /* 动态背景效果 */
        .background-effect {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
            opacity: 0.3;
        }

        .left .background-effect {
            background: radial-gradient(circle at 30% 50%, rgba(255, 0, 0, 0.8), transparent 70%),
                        radial-gradient(circle at 70% 70%, rgba(255, 0, 0, 0.6), transparent 60%);
            animation: pulse-bg 8s ease-in-out infinite alternate;
        }

        .right .background-effect {
            background: radial-gradient(circle at 70% 50%, rgba(0, 0, 255, 0.8), transparent 70%),
                        radial-gradient(circle at 30% 70%, rgba(0, 0, 255, 0.6), transparent 60%);
            animation: pulse-bg 8s ease-in-out infinite alternate-reverse;
        }

        @keyframes pulse-bg {
            0% { opacity: 0.2; transform: scale(0.95); }
            100% { opacity: 0.4; transform: scale(1.05); }
        }

        /* 大幅度变化特效 */
        .significant-change .side {
            animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
        }

        @keyframes shake {
            10%, 90% { transform: translate3d(-1px, 0, 0); }
            20%, 80% { transform: translate3d(2px, 0, 0); }
            30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
            40%, 60% { transform: translate3d(4px, 0, 0); }
        }

        /* 移除闪光效果 */

        .error-message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 0, 0, 0.8);
            padding: 10px 20px;
            border-radius: 4px;
            z-index: 100;
        }

        .error-message button {
            margin-left: 10px;
            padding: 4px 8px;
            cursor: pointer;
        }


    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 错误提示 -->
            <div v-if="error" class="error-message">
                {{error}}
                <button @click="retryFetch">重试</button>
            </div>


            <!-- VS标志 -->
            <div class="vs-badge">
                <span class="vs-text">V</span>

                <span class="vs-text">S</span>
                <div class="vs-light-beam"></div>
            </div>

            <!-- 中间分隔线 -->
            <div class="divider"></div>

            <!-- 左侧（正方） -->
            <div class="side left" :style="{flex: adjustedLeftFlex}">
                <div class="left-glow"></div> <!-- 添加光晕元素 -->
                <div class="label">正方</div>
                <div class="percentage left-text" :class="{changed: leftChanged}"
                     :style="{'--left-scale': leftPercentage/50}">
                    {{leftPercentage}}%
                </div>
                <div class="count" :class="{changed: leftChanged}">{{leftCount}}票</div>
            </div>

            <!-- 右侧（反方） -->
            <div class="side right" :style="{flex: adjustedRightFlex}">
                <div class="right-glow"></div> <!-- 添加光晕元素 -->
                <div class="label">反方</div>
                <div class="percentage right-text" :class="{changed: rightChanged}"
                     :style="{'--right-scale': rightPercentage/50}">
                    {{rightPercentage}}%
                </div>
                <div class="count" :class="{changed: rightChanged}">{{rightCount}}票</div>
            </div>
        </div>
        <div class="total">总票数: {{total}}</div>
    </div>

    <script>
        const { createApp } = Vue

        // API配置常量
        const API_CONFIG = {
            URL: 'https://noco.ohvfx.com/api/v2/tables/m7kssbxv4srhmqb/records',
            TOKEN: 'bF-rl7NY_i_A-6aO1_PN0rbsm6cHkPf3bSK0ildp',
            POLL_INTERVAL: 3000, // 轮询间隔，单位毫秒（改为1秒一次）
        };

        createApp({
            data() {
                return {
                    leftCount: 0,
                    rightCount: 0,
                    leftPercentage: 50,
                    rightPercentage: 50,
                    total: 0,
                    leftChanged: false,
                    rightChanged: false,
                    error: null,
                    retryCount: 0,
                    maxRetries: 3,
                    isLoading: false,
                    pollTimer: null,
                    lastDataHash: null // 用于检测数据是否有变化
                }
            },
            computed: {
                // 构建API URL
                apiUrl() {
                    return API_CONFIG.URL;
                },
                // 调整后的左侧flex值，确保在极端情况下仍然有足够空间
                adjustedLeftFlex() {
                    // 当百分比小于20%时，保持最小值为20
                    return Math.max(20, this.leftPercentage);
                },
                // 调整后的右侧flex值
                adjustedRightFlex() {
                    // 当百分比小于20%时，保持最小值为20
                    return Math.max(20, this.rightPercentage);
                }
            },
            methods: {
                // 生成数据的哈希值，用于比较数据是否变化
                generateDataHash(leftCount, rightCount) {
                    return `${leftCount}-${rightCount}`;
                },

                // 防抖动的API请求函数
                async fetchDataWithDebounce() {
                    if (this.isLoading) return; // 如果正在加载，则跳过

                    this.isLoading = true;
                    await this.fetchData();
                    this.isLoading = false;
                },

                // 主要数据获取函数
                async fetchData() {
                    try {
                        this.error = null;
                        const response = await fetch(this.apiUrl, {
                            method: 'GET',
                            headers: {
                                'xc-token': API_CONFIG.TOKEN,
                                'Content-Type': 'application/json'
                            }
                        })

                        if (!response.ok) {
                            throw new Error(`网络请求失败: ${response.status} ${response.statusText}`);
                        }

                        const data = await response.json()

                        // 处理API返回的数据格式
                        let leftCount = 0;
                        let rightCount = 0;

                        // 遍历记录找到正方和反方的最终得分
                        if (data.list) {
                            data.list.forEach(record => {
                                if (record.队伍 === '正方') {
                                    leftCount = parseInt(record.最终得分) || 0;
                                } else if (record.队伍 === '反方') {
                                    rightCount = parseInt(record.最终得分) || 0;
                                }
                            });
                        }

                        // 生成当前数据的哈希值
                        const currentDataHash = this.generateDataHash(leftCount, rightCount);

                        // 检查数据是否有变化
                        if (currentDataHash !== this.lastDataHash) {
                            // 计算总票数和百分比
                            const total = leftCount + rightCount;
                            const leftPercentage = total > 0 ? parseFloat((leftCount / total * 100).toFixed(1)) : 50;
                            const rightPercentage = total > 0 ? parseFloat((rightCount / total * 100).toFixed(1)) : 50;

                            // 检查数据变化并触发动画
                            if(this.leftPercentage != leftPercentage) {
                                this.leftChanged = true
                                setTimeout(() => this.leftChanged = false, 600)
                            }
                            if(this.rightPercentage != rightPercentage) {
                                this.rightChanged = true
                                setTimeout(() => this.rightChanged = false, 600)
                            }

                            // 移除大幅度变化的特效代码

                            // 更新数据
                            this.leftCount = leftCount
                            this.rightCount = rightCount
                            this.leftPercentage = leftPercentage
                            this.rightPercentage = rightPercentage
                            this.total = total

                            // 更新VS标志位置，确保在极端比分下仍然保持在可见范围内
                            // 计算安全的分隔线位置，限制在 20% 到 80% 之间
                            const safePosition = Math.max(20, Math.min(80, leftPercentage));
                            document.documentElement.style.setProperty('--divider-position', `${safePosition}%`);

                            // 更新数据哈希值
                            this.lastDataHash = currentDataHash;

                            console.log(`数据已更新: 正方${leftCount}票(${leftPercentage}%), 反方${rightCount}票(${rightPercentage}%)`);
                        } else {
                            console.log('数据未变化，跳过更新');
                        }

                        this.retryCount = 0; // 重置重试计数
                    } catch (error) {
                        console.error('获取数据失败:', error);
                        this.error = `获取数据失败: ${error.message}`;

                        if (this.retryCount < this.maxRetries) {
                            this.retryCount++;
                            const retryDelay = 1000 * Math.pow(2, this.retryCount - 1); // 指数退避策略
                            console.log(`将在 ${retryDelay/1000} 秒后重试...`);
                            setTimeout(this.fetchData, retryDelay);
                        }
                    }
                },

                // 手动重试
                retryFetch() {
                    this.retryCount = 0;
                    this.error = null;
                    this.fetchData();
                },

                // 开始轮询
                startPolling() {
                    this.stopPolling(); // 确保先停止任何现有的轮询
                    this.pollTimer = setInterval(this.fetchDataWithDebounce, API_CONFIG.POLL_INTERVAL);
                    console.log(`开始轮询，间隔 ${API_CONFIG.POLL_INTERVAL/1000} 秒`);
                },

                // 停止轮询
                stopPolling() {
                    if (this.pollTimer) {
                        clearInterval(this.pollTimer);
                        this.pollTimer = null;
                        console.log('轮询已停止');
                    }
                }
            },
            mounted() {
                console.log('组件已挂载，初始化数据...');
                this.fetchData().then(() => {
                    this.startPolling(); // 初始数据加载完成后开始轮询
                });
            },
            unmounted() {
                // 组件卸载时清理资源
                this.stopPolling();
                console.log('组件已卸载，清理资源完成');
            }
        }).mount('#app')
    </script>
</body>
</html>